# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

{{- /* Check if the neuvector-secret exists and get the admin password from it */}}
{{- $neuvectorAdminPass := ""}}
{{- /* Try to lookup existing neuvector-secret */}}
{{- $secret := (lookup "v1" "Secret" "neuvector" "neuvector-secret") }}
{{- if $secret }}
  {{- /* If we found the secret, try to extract the admin password from userinitcfg.yaml */}}
  {{- if (index $secret "data" "userinitcfg.yaml") }}
    {{- $secretData := (get $secret.data "userinitcfg.yaml" | b64dec | fromYaml) }}
    {{- if $secretData }}
      {{- if $secretData.users }}
        {{- range $user := $secretData.users }}
          {{- if eq $user.username "admin" }}
            {{- $neuvectorAdminPass = $user.password }}
          {{- end }}
        {{- end }}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
{{- /* Generate a new password if we couldn't find an existing one */}}
{{- if eq $neuvectorAdminPass "" }}
  {{- $neuvectorAdminPass = join "" (list (randAlphaNum 12) (randAlpha 2 | upper) (randAlpha 2 | lower) (randNumeric 2)) }}
{{- end }}

apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: neuvector
  namespace: {{ .Release.Namespace }}
spec:
  # This is disabled pending further discussion/upstream changes to handle metrics with SSO setup
  # monitor:
  #   - selector:
  #       app: neuvector-prometheus-exporter
  #     podSelector:
  #       app: neuvector-prometheus-exporter-pod
  #     portName: metrics
  #     targetPort: 8068
  #     description: "Metrics"

  sso:
    - name: NeuVector
      clientId: uds-core-admin-neuvector
      redirectUris:
        {{- if .Values.adminDomain }}
        - "https://neuvector.{{ .Values.adminDomain }}/openId_auth"
        {{- else }}
        - "https://neuvector.admin.{{ .Values.domain }}/openId_auth"
        {{- end }}
      secretName: neuvector-secret
      secretLabels:
        uds.dev/pod-reload: "true"
      secretAnnotations:
        uds.dev/pod-reload-selector: 'app=neuvector-controller-pod'
      secretTemplate:
        userinitcfg.yaml: |-
          always_reload: true
          users:
            - username: admin
              fullname: admin
              password: {{ $neuvectorAdminPass }}
              role: admin
        oidcinitcfg.yaml: |-
          always_reload: true
          client_id: clientField(clientId)
          client_secret: clientField(secret)
          enable: true
          group_claim: groups
          issuer: https://sso.{{ .Values.domain }}/realms/uds
          group_mapped_roles:
            - group: /UDS Core/Admin
              global_role: admin
            - group: /UDS Core/Auditor
              global_role: reader

  network:
    serviceMesh:
      mode: "ambient"

    expose:
      - service: neuvector-service-webui
        selector:
          app: neuvector-manager-pod
        gateway: admin
        host: neuvector
        port: 8443

    allow:
      # Permit intra-namespace communication
      - direction: Ingress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: IntraNamespace

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: neuvector-controller-pod

      # Access to SSO for OIDC
      - direction: Egress
        selector:
          app: neuvector-controller-pod
        remoteSelector:
          app: tenant-ingressgateway
        remoteNamespace: istio-tenant-gateway
        description: "SSO Provider"

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: neuvector-updater-pod

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: neuvector-cert-upgrader-pod

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: neuvector-scanner-pod

      - direction: Egress
        remoteGenerated: KubeAPI
        selector:
          app: neuvector-enforcer-pod

      - direction: Ingress
        # todo: evaluate a "KubeAPI" _ingress_ generated rule for webhook calls
        remoteGenerated: Anywhere
        selector:
          app: neuvector-controller-pod
        port: 30443
        description: "Webhook"

      - direction: Ingress
        remoteNamespace: istio-admin-gateway
        remoteSelector:
          app.kubernetes.io/name: admin-ingressgateway
        remoteServiceAccount: admin-ingressgateway
        selector:
          app: neuvector-manager-waypoint
        port: 8443
        description: waypoint-admin-gateway

      - direction: Egress
        selector:
          app: neuvector-manager-waypoint
        remoteNamespace: istio-system
        remoteSelector:
          app: istiod
        port: 15012
        description: Waypoint to Istiod

      - direction: Ingress
        selector:
          app: neuvector-manager-waypoint
        remoteNamespace: monitoring
        remoteSelector:
          app.kubernetes.io/name: prometheus
        remoteServiceAccount: kube-prometheus-stack-prometheus
        port: 15020
        description: Waypoint Metrics

      # Custom rules for additional networking access
      {{- with .Values.additionalNetworkAllow }}
      {{ toYaml . | nindent 6 }}
      {{- end }}
