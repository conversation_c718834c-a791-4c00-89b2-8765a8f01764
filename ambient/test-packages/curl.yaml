apiVersion: v1
kind: Namespace
metadata:
  name: curl-ns
---
# Global Waypoint in istio-egress
apiVersion: uds.dev/v1alpha1
kind: Package
metadata:
  name: pkg-1
  namespace: curl-ns
spec:
  network: 
    serviceMesh:
      mode: ambient
    allow: 
      - direction: Egress
        selector:
          app: curl1
        remoteHost: httpbin.org
        remoteProtocol: TLS
        port: 443
        description: httpbin
        serviceAccount: curl1
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: curl1
  namespace: curl-ns
---
apiVersion: v1
kind: Service
metadata:
  name: curl1
  namespace: curl-ns
  labels:
    app: curl1
    service: curl1
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: curl1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl1
  namespace: curl-ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl1
  template:
    metadata:
      labels:
        app: curl1
    spec:
      terminationGracePeriodSeconds: 0
      serviceAccountName: curl1
      containers:
      - name: curl
        image: curlimages/curl
        command: ["/bin/sleep", "infinity"]
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /etc/curl/tls
          name: secret-volume
      volumes:
      - name: secret-volume
        secret:
          secretName: curl-secret
          optional: true
---
# Disallowed curl
apiVersion: v1
kind: ServiceAccount
metadata:
  name: curl2
  namespace: curl-ns
---
apiVersion: v1
kind: Service
metadata:
  name: curl2
  namespace: curl-ns
  labels:
    app: curl2
    service: curl2
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: curl2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl2
  namespace: curl-ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl2
  template:
    metadata:
      labels:
        app: curl2
    spec:
      terminationGracePeriodSeconds: 0
      serviceAccountName: curl2
      containers:
      - name: curl
        image: curlimages/curl
        command: ["/bin/sleep", "infinity"]
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /etc/curl/tls
          name: secret-volume
      volumes:
      - name: secret-volume
        secret:
          secretName: curl-secret
          optional: true
