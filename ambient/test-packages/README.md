# Testing

## Setup

Run to setup uds-core with istio-egress-gateway 
```bash
uds run dev-setup --with istio_components=istio-egress-gateway
```

Run to deploy pepr
```bash
npx pepr dev
```

## Tests

### Basic curl package

1) Apply curl.yaml
* Verify curl1 can access httpbin.org
* Verify curl2 cannot access httpbin.org

2) Switch curl.yaml package from ambient -> sidecar 
* Validate shared waypoint + resources is gone
* Validate new sidecar egress resources are created
* Validate access as before

3) Switch curl.yaml package from sidecar -> ambient 
* Validate shared sidecar egress resources are gone
* Validate new waypoint is back
* Validate access as before

### Multiple packages reconciling

1) Apply multiple.yaml
