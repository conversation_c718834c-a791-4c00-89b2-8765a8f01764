{"name": "uds-core", "version": "0.6.0", "description": "A collection of capabilities for UDS Core", "keywords": ["pepr", "k8s", "policy-engine", "pepr-module", "security"], "engines": {"node": ">=20.0.0"}, "pepr": {"name": "UDS Core", "uuid": "uds-core", "onError": "reject", "logLevel": "info", "alwaysIgnore": {"namespaces": ["zarf"], "labels": []}}, "scripts": {"k3d-setup": "k3d cluster delete pepr-dev && k3d cluster create pepr-dev --k3s-arg '--debug@server:0'"}, "dependencies": {"pepr": "0.51.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@types/ws": "^8.18.1", "globals": "^16.2.0", "husky": "9.1.7", "lint-staged": "16.1.2", "vitest": "^3.0.0"}}