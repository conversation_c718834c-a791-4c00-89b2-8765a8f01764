# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: core-monitoring
  description: "UDS Core Monitoring (Prometheus and Grafana)"
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.46.0"
  # x-release-please-end
  x-uds-dependencies: ["base", "identity-authorization"]
  annotations:
    dev.uds.title: UDS Core (Monitoring)
    dev.uds.tagline: Enables frontend log data, metrics monitoring, and alerting
    dev.uds.category: UDS-Core,Monitoring
    dev.uds.keywords: uds,prometheus,grafana

components:
  # Prometheus
  - name: kube-prometheus-stack
    required: true
    import:
      path: ../../src/prometheus-stack
  # Grafana
  - name: grafana
    required: true
    import:
      path: ../../src/grafana
