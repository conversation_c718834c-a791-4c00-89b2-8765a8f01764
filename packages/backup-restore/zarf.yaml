# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: core-backup-restore
  description: "UDS Core (Backup and Restore)"
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.46.0"
  # x-release-please-end
  x-uds-dependencies: ["base"]
  annotations:
    dev.uds.title: UDS Core (Backup and Restore)
    dev.uds.tagline: Enable volumes and Kubernetes objects to be backed up and restored
    dev.uds.category: UDS-Core, Backup, Restore
    dev.uds.keywords: uds,backup,restore,velero

components:
  # Velero
  - name: velero
    required: true
    import:
      path: ../../src/velero
