# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: k3d-core-slim-dev
  description: "Rehydratable UDS K3d + UDS Core Slim (Istio, UDS Operator and Keycloak) Checkpoint"
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.46.0"
  # x-release-please-end

variables:
  - name: CLUSTER_NAME
    description: "Name of the cluster"
    default: "uds"

  - name: K3D_EXTRA_ARGS
    description: "Optionally pass k3d arguments to the default"
    default: ""

  - name: NGINX_EXTRA_PORTS
    description: "Optionally allow more ports through Nginx (combine with K3D_EXTRA_ARGS '-p <port>:<port>@server:*')"
    default: "[]"

components:
  - name: destroy-cluster
    required: true
    description: "Optionally destroy the cluster before creating it"
    actions:
      onDeploy:
        before:
          - cmd: |
              echo "This package requires elevated permissions to deploy - requesting sudo (if paused enter password)"
              sudo echo "got sudo! success!"
          - cmd: k3d cluster delete ${ZARF_VAR_CLUSTER_NAME}
            description: "Destroy the cluster"
          - cmd: |
              sudo rm -rf data
          - cmd: |
              if [ -z "$TMPDIR" ]; then
                #  macOS sets TMPDIR to a user temp directory - this also provides more options to linux
                TMPDIR="/tmp"
              fi
              DATA_DIR="${TMPDIR}/uds-checkpoint-data"
              sudo rm -rf "$DATA_DIR"

  - name: create-cluster
    required: true
    description: "Create the K3d cluster w/UDS Core pre-installed"
    files:
      - source: uds-checkpoint.tar
        target: uds-checkpoint.tar
    actions:
      onCreate:
        before:
          - description: "Scale down ztunnel, pepr, and keycloak before snapshot"
            cmd: |
              set -e
              ./zarf tools kubectl -n keycloak scale statefulset keycloak --replicas=0
              ./zarf tools kubectl rollout status -n keycloak statefulset keycloak --timeout=60s

              ./zarf tools kubectl -n pepr-system scale deploy pepr-uds-core-watcher --replicas=0
              ./zarf tools kubectl rollout status -n pepr-system deploy pepr-uds-core-watcher --timeout=60s

              ./zarf tools kubectl -n pepr-system scale deploy pepr-uds-core --replicas=0
              ./zarf tools kubectl rollout status -n pepr-system deploy pepr-uds-core --timeout=60s

              ./zarf tools kubectl -n istio-system patch daemonset ztunnel --type='strategic' -p '{"spec":{"template":{"spec":{"nodeSelector":{"no-such-label":"true"}}}}}'
              ./zarf tools kubectl -n istio-system wait --for=delete pod -l app=ztunnel --timeout=60s

          - cmd: ./checkpoint.sh
        onSuccess:
          - cmd: |
              if [ -z "$TMPDIR" ]; then
                #  macOS sets TMPDIR to a user temp directory - this also provides more options to linux
                TMPDIR="/tmp"
              fi
              DATA_DIR="${TMPDIR}/uds-checkpoint-data"
              sudo rm -rf "$DATA_DIR" uds-checkpoint.tar
      onDeploy:
        after:
          - cmd: |
              if [ -z "$TMPDIR" ]; then
                #  macOS sets TMPDIR to a user temp directory - this also provides more options to linux
                TMPDIR="/tmp"
              fi
              DATA_DIR="${TMPDIR}/uds-checkpoint-data"
              mkdir -p "$DATA_DIR"

              sudo tar --blocking-factor=64 -xpf uds-checkpoint.tar -C "$DATA_DIR"
              K8S_TOKEN="$(sudo cat ${DATA_DIR}/k3s_data/server/token)"
              echo $K8S_TOKEN
              sudo docker load -i "${DATA_DIR}/uds-k3d-checkpoint-latest.tar"

              k3d cluster create \
                -p "80:80@server:*" \
                -p "443:443@server:*" \
                --api-port 6550 \
                --k3s-arg "--disable=traefik@server:*" \
                --k3s-arg "--disable=metrics-server@server:*" \
                --k3s-arg "--disable=servicelb@server:*" \
                --k3s-arg "--disable=local-storage@server:*" \
                --k3s-arg "--token=${K8S_TOKEN}@server:*" \
                -v "${DATA_DIR}/kubelet_data:/var/lib/kubelet@server:*" \
                -v "${DATA_DIR}/k3s_data:/var/lib/rancher/k3s@server:*" \
                --image ghcr.io/defenseunicorns/uds-core/checkpoint:latest ${ZARF_VAR_K3D_EXTRA_ARGS} \
                ${ZARF_VAR_CLUSTER_NAME}
            description: "Create the cluster"

          - description: "Scale up ztunnel, pepr, and keycloak after checkpoint restore"
            cmd: |
              set -e
              ./zarf tools kubectl patch mutatingwebhookconfiguration pepr-uds-core --type='json' -p='[{"op":"replace","path":"/webhooks/0/failurePolicy","value":"Ignore"}]'
              ./zarf tools kubectl patch validatingwebhookconfiguration pepr-uds-core --type='json' -p='[{"op":"replace","path":"/webhooks/0/failurePolicy","value":"Ignore"}]'

              ./zarf tools kubectl -n istio-system patch daemonset ztunnel --type=json -p='[{"op":"remove","path":"/spec/template/spec/nodeSelector/no-such-label"}]'
              ./zarf tools kubectl -n istio-system rollout status daemonset ztunnel --timeout=60s

              ./zarf tools kubectl -n pepr-system scale deploy pepr-uds-core --replicas=2
              ./zarf tools kubectl rollout status -n pepr-system deploy pepr-uds-core --timeout=60s

              ./zarf tools kubectl patch mutatingwebhookconfiguration pepr-uds-core --type='json' -p='[{"op":"replace","path":"/webhooks/0/failurePolicy","value":"Fail"}]'
              ./zarf tools kubectl patch validatingwebhookconfiguration pepr-uds-core --type='json' -p='[{"op":"replace","path":"/webhooks/0/failurePolicy","value":"Fail"}]'

              ./zarf tools kubectl -n pepr-system scale deploy pepr-uds-core-watcher --replicas=1
              ./zarf tools kubectl -n pepr-system rollout status deploy pepr-uds-core-watcher --timeout=60s

              ./zarf tools kubectl -n keycloak scale statefulset keycloak --replicas=1
              ./zarf tools kubectl -n keycloak rollout status statefulset keycloak --timeout=180s

        onSuccess:
          - cmd: rm -f uds-checkpoint.tar
