# Copyright 2024 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: ZarfPackageConfig
metadata:
  name: core
  description: "UDS Core is a collection of several individual applications combined into a single Zarf Package, that establishes a secure baseline for secure cloud-native systems."
  authors: "Defense Unicorns - Product"
  # x-release-please-start-version
  version: "0.46.0"
  # x-release-please-end
  annotations:
    dev.uds.title: UDS Core
    dev.uds.tagline: Collection of packages that enable a secure baseline for cloud-native systems
    dev.uds.category: UDS-Core
    dev.uds.keywords: uds,core

components:
  - name: uds-operator-config
    required: true
    import:
      path: ../base

  # CRDs
  - name: prometheus-operator-crds
    required: true
    import:
      path: ../base

  # Pepr the world
  - name: pepr-uds-core
    required: true
    import:
      path: ../base

  # Istio
  - name: istio-controlplane
    required: true
    import:
      path: ../base

  - name: gateway-api-crds
    required: true
    import:
      path: ../../src/istio/common

  - name: istio-admin-gateway
    required: true
    import:
      path: ../base

  - name: istio-tenant-gateway
    required: true
    import:
      path: ../base

  - name: istio-egress-waypoint
    required: true
    import:
      path: ../base

  - name: istio-passthrough-gateway
    required: false
    import:
      path: ../base

  - name: istio-egress-gateway
    required: false
    import:
      path: ../base

  # Metrics Server
  - name: metrics-server
    required: false
    import:
      path: ../metrics-server

  # Keycloak
  - name: keycloak
    required: true
    import:
      path: ../identity-authorization

  # Neuvector
  - name: neuvector
    required: true
    import:
      path: ../runtime-security

  # Loki
  - name: loki
    required: true
    import:
      path: ../logging

  # Prometheus
  - name: kube-prometheus-stack
    required: true
    import:
      path: ../monitoring

  # Vector
  - name: vector
    required: true
    import:
      path: ../logging

  # Grafana
  - name: grafana
    required: true
    import:
      path: ../monitoring

  # Authservice
  - name: authservice
    required: true
    import:
      path: ../identity-authorization

  # Velero
  - name: velero
    required: true
    import:
      path: ../backup-restore
