# Copyright 2025 Defense Unicorns
# SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial

kind: UDSBundle
metadata:
  name: base-shim
  description: A shim bundle for deploying the base package with overrides in CI/Dev environments
  # x-release-please-start-version
  version: "0.46.0"
  # x-release-please-end

packages:
  - name: core-base
    path: ../../build/
    # x-release-please-start-version
    ref: 0.46.0
    # x-release-please-end
    optionalComponents:
      - istio-passthrough-gateway
      - istio-egress-gateway
    overrides:
      pepr-uds-core:
        module:
          values:
            - path: additionalIgnoredNamespaces
              value:
                - uds-dev-stack
            - path: "watcher.resources.requests.memory"
              value: "64Mi"
            - path: "admission.resources.requests.memory"
              value: "64Mi"
            - path: "watcher.resources.requests.cpu"
              value: "100m"
            - path: "admission.resources.requests.cpu"
              value: "100m"
      istio-controlplane:
        istiod:
          values:
            - path: "resources.requests.memory"
              value: "1024Mi"
            - path: "resources.requests.cpu"
              value: "100m"
            - path: "global.proxy.resources.requests.memory"
              value: "40Mi"
            - path: "global.proxy.resources.limits.memory"
              value: "1024Mi"
            - path: "global.proxy.resources.requests.cpu"
              value: "10m"
            - path: "global.proxy.resources.limits.cpu"
              value: "2000m"
